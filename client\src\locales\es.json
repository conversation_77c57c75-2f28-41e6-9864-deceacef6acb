{"meta": {"title": "Text Case Converter - Herramienta Moderna de Conversión de Casos de Texto", "description": "Text Case Converter es una potente herramienta de conversión de texto que te ayuda a transformar texto a mayúsculas, minúsculas, título y más. Compatible con varios idiomas."}, "language": {"selectLanguage": "Idioma"}, "navigation": {"textCaseConversion": "Conversión de Casos", "specialFormatting": "Formato Especial", "codeAndData": "Código y Datos", "translation": "Traducción", "tools": "Herramientas", "settings": "Configuración"}, "converter": {"title": "Herramienta de Conversión de Casos de Texto", "introduction": "¿Dejaste accidentalmente el bloq mayús activado y escribiste algo, pero no quieres volver a empezar y reescribirlo todo?", "instructions": "Simplemente ingresa tu texto y elige el caso al que deseas convertirlo.", "placeholder": "Escribe o pega tu contenido aquí..."}, "cases": {"sentence": "Caso oración", "lower": "minúsculas", "upper": "MAYÚSCULAS", "capitalized": "Cada Palabra Mayúscula", "alternating": "cAsO AlTeRnAdO", "title": "<PERSON><PERSON><PERSON>", "inverse": "CaSo InVeRtIdO"}, "actions": {"download": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "clear": "Limpiar", "support": "Apoyar Este Proyecto"}, "stats": {"characterCount": "Recuento de Caracteres", "wordCount": "Recuento de Palabras", "sentenceCount": "Recuento de Oraciones", "lineCount": "Recuento de Líneas"}, "explanations": {"sectionTitle": "Tipos de Conversión del Text Case Converter", "sentence": {"title": "Caso oración", "description1": "El convertidor de caso oración te permitirá pegar cualquier texto que desees, y lo transformará automáticamente en una oración estructurada completamente formada.", "description2": "Funciona capitalizando la primera letra de cada oración, y luego continúa transformando el resto del texto en minúsculas, así como convirtiendo las i's en I's.", "example": "Este es un ejemplo de caso oración."}, "lower": {"title": "minúsculas", "description": "Si te preguntas cómo descapitalizar texto, esto es exactamente lo que el convertidor de texto en minúsculas te permitirá hacer - transforma todas las letras de tu texto en letras minúsculas.", "example": "este es un ejemplo de minúsculas."}, "upper": {"title": "MAYÚSCULAS", "description": "El transformador de mayúsculas tomará cualquier texto que tengas y generará todas las letras en mayúsculas. Esencialmente convertirá todas las letras minúsculas en MAYÚSCULAS.", "example": "ESTE ES UN EJEMPLO DE MAYÚSCULAS."}, "capitalized": {"title": "Cada Palabra Mayúscula", "description": "El convertidor de caso capitalizado convertirá automáticamente la letra inicial de cada palabra en mayúscula y dejará las letras restantes en minúsculas.", "example": "Este Es Un Ejemplo De Caso Capitalizado."}, "alternating": {"title": "cAsO AlTeRnAdO", "description": "El convertidor de caso alternado te permitirá transformar tu texto en un texto que alterna entre minúsculas y mayúsculas. Generará una letra mayúscula y luego una letra minúscula dentro de la misma palabra.", "example": "eStE Es uN EjEmPlO De cAsO AlTeRnAdO."}, "title": {"title": "<PERSON><PERSON><PERSON>", "description": "El convertidor de caso título es perfecto para aquellos que no están seguros de cómo titular un próximo ensayo. Asegura que las letras correctas estén en mayúsculas dentro del contexto de un título.", "example": "Este Es un Ejemplo de Caso Título."}}, "additionalTools": {"title": "Herramientas de Texto Adicionales", "viewAll": "Ver todas las herramientas de texto", "reverse": {"title": "Generador de Texto Invertido", "description": "Voltea tu texto hacia atrás"}, "upsideDown": {"title": "Texto Al Revés", "description": "Voltea tu texto boca abajo"}, "morseCode": {"title": "Traduc<PERSON> <PERSON>ódigo <PERSON>", "description": "Convierte texto a código morse"}, "smallText": {"title": "Generador de Texto Pequeño", "description": "Crea texto estilizado pequeño"}, "strikethrough": {"title": "<PERSON><PERSON>", "description": "Añade una línea a través de tu texto"}, "wideText": {"title": "Generador de Texto Ancho", "description": "Crea texto con espaciado estético"}}, "faq": {"title": "Preguntas Frecuentes", "why": {"question": "¿Por qué necesitaría convertir casos de texto?", "answer": "La conversión de casos de texto es útil para formatear documentos, preparar títulos, corregir texto accidentalmente en mayúsculas o mantener un estilo consistente en tu contenido."}, "secure": {"question": "¿Están seguros mis datos de texto al usar esta herramienta?", "answer": "Absolutamente. Todo el procesamiento de texto ocurre directamente en tu navegador. Nunca almacenamos, transmitimos o accedemos al texto que ingresas."}, "mobile": {"question": "¿Puedo usar esta herramienta en mi dispositivo móvil?", "answer": "Sí, nuestro convertidor de casos de texto es completamente responsive y funciona en todos los dispositivos, incluyendo smartphones, tablets y computadoras de escritorio."}, "languages": {"question": "¿Esta herramienta funciona con idiomas distintos al inglés?", "answer": "Sí, nuestra herramienta es compatible con múltiples idiomas y conjuntos de caracteres internacionales, incluidos caracteres acentuados, cirílico, griego y muchos otros."}}, "footer": {"description": "Una colección completa de herramientas de transformación de texto para ayudarte a formatear y optimizar tu contenido.", "popularTools": "Herramientas Populares", "resources": "Recursos", "language": "Idioma", "allRightsReserved": "Todos los derechos reservados.", "privacyPolicy": "Política de Privacidad", "termsOfService": "Términos de Servicio", "contactUs": "Contáctanos"}, "tools": {"textCaseConverter": "Convertidor de Casos de Texto", "loremIpsumGenerator": "Generador de Lorem I<PERSON>um", "characterCountTool": "Con<PERSON><PERSON>cteres", "wordCountTool": "Contador de Palabras", "reverseTextGenerator": "Generador de Texto Invertido"}, "resources": {"blog": "Blog", "helpCenter": "Centro de Ayuda", "apiDocumentation": "Documentación de API", "suggestTool": "Sugerir una Herramienta", "chromeExtension": "Extensión de Chrome"}, "toast": {"copied": "Texto Co<PERSON>do", "copiedDescription": "El texto ha sido copiado al portapapeles", "copyFailed": "<PERSON><PERSON><PERSON> al Copiar", "copyFailedDescription": "No se pudo copiar el texto al portapapeles", "downloaded": "<PERSON><PERSON><PERSON>", "downloadedDescription": "El texto ha sido descargado como archivo", "cleared": "<PERSON><PERSON><PERSON>", "clearedDescription": "El texto ha sido borrado"}}