import { Routes, Route, useParams } from "react-router-dom";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import PrivacyPolicy from "@/pages/privacy-policy";
import TermsOfService from "@/pages/terms-of-service";
import Header from "@/components/Header";
import SimpleFooter from "@/components/SimpleFooter";
import AppWrapper from "@/components/AppWrapper";
import { HelmetProvider } from 'react-helmet-async';

// 语言路由包装组件
function LangHomeRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <Home />;
}

function LangPrivacyRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <PrivacyPolicy />;
}

function LangTermsRoute() {
  const { lang } = useParams();
  const validLangs = ['en', 'es', 'fr', 'de', 'zh'];

  if (!validLangs.includes(lang || '')) {
    return <NotFound />;
  }

  return <TermsOfService />;
}

function Router() {
  return (
    <Routes>
      {/* 默认路由（英文） */}
      <Route path="/" element={<Home />} />
      <Route path="/privacy-policy" element={<PrivacyPolicy />} />
      <Route path="/terms-of-service" element={<TermsOfService />} />

      {/* 语言特定路由 */}
      <Route path="/:lang" element={<LangHomeRoute />} />
      <Route path="/:lang/privacy-policy" element={<LangPrivacyRoute />} />
      <Route path="/:lang/terms-of-service" element={<LangTermsRoute />} />

      {/* 404页面 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <HelmetProvider>
        <AppWrapper>
          <TooltipProvider>
            <Toaster />
            <div className="flex flex-col min-h-screen">
              <Header />
              <div className="flex-grow">
                <Router />
              </div>
              <div className="container mx-auto px-4 mb-6">
                <SimpleFooter />
              </div>
            </div>
          </TooltipProvider>
        </AppWrapper>
      </HelmetProvider>
    </QueryClientProvider>
  );
}

export default App;
