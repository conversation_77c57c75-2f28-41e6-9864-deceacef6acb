import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useToast } from "@/hooks/use-toast";
import { getTextStats, convertTextCase, TextStats } from '../utils/textUtils';
import { downloadTextAsFile, copyToClipboard } from '../utils/fileUtils';
import { Textarea } from "@/components/ui/textarea";
import { useLanguageSync } from '@/hooks/use-language-sync';

type Case = 'sentence' | 'lower' | 'upper' | 'capitalized' | 'alternating' | 'title' | 'inverse';

const TextConverter = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  // 使用自定义hook确保语言同步
  const { currentLanguage } = useLanguageSync('TextConverter');

  const [text, setText] = useState('');
  const [textStats, setTextStats] = useState<TextStats>({
    characterCount: 0,
    wordCount: 0,
    sentenceCount: 0,
    lineCount: 0
  });
  const [convertedText, setConvertedText] = useState('');
  const [selectedCase, setSelectedCase] = useState<Case>('sentence');

  useEffect(() => {
    setTextStats(getTextStats(text));
  }, [text]);

  useEffect(() => {
    // 当文本改变或选择的大小写类型改变时，更新转换后的文本
    if (text) {
      setConvertedText(convertTextCase(text, selectedCase));
    } else {
      setConvertedText('');
    }
  }, [text, selectedCase]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);
  };

  const handleCaseChange = (caseType: Case) => {
    setSelectedCase(caseType);
  };

  const handleCopy = async () => {
    try {
      await copyToClipboard(convertedText);
      toast({
        title: t('toast.copied'),
        description: t('toast.copiedDescription'),
      });
    } catch (error) {
      toast({
        title: t('toast.copyFailed'),
        description: t('toast.copyFailedDescription'),
        variant: "destructive",
      });
    }
  };

  const handleDownload = () => {
    downloadTextAsFile(convertedText, 'converted-text.txt');
    toast({
      title: t('toast.downloaded'),
      description: t('toast.downloadedDescription'),
    });
  };

  const handleClear = () => {
    setText('');
    setConvertedText('');
    toast({
      title: t('toast.cleared'),
      description: t('toast.clearedDescription'),
    });
  };

  // 为不同语言优化示例占位符文本
  const getPlaceholderText = () => {
    if (currentLanguage === 'zh') {
      return t('converter.placeholder');
    } else if (currentLanguage === 'es') {
      return t('converter.placeholder');
    } else if (currentLanguage === 'fr') {
      return t('converter.placeholder');
    } else if (currentLanguage === 'de') {
      return t('converter.placeholder');
    }
    return t('converter.placeholder');
  };

  return (
    <section className="mb-8">
      <h1 className="text-2xl font-bold mb-4 text-center">{t('converter.title')}</h1>
      <p className="mb-2 text-center">{t('converter.introduction')}</p>
      <p className="mb-6 text-center">{t('converter.instructions')}</p>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <Textarea
            placeholder={getPlaceholderText()}
            className="w-full h-64 mb-4 p-3 border border-gray-300 rounded"
            value={text}
            onChange={handleTextChange}
          />

          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-2 xl:grid-cols-3 gap-2 mb-4">
            <div className="text-sm">
              <span className="font-bold">{t('stats.characterCount')}:</span> {textStats.characterCount}
            </div>
            <div className="text-sm">
              <span className="font-bold">{t('stats.wordCount')}:</span> {textStats.wordCount}
            </div>
            <div className="text-sm">
              <span className="font-bold">{t('stats.sentenceCount')}:</span> {textStats.sentenceCount}
            </div>
            <div className="text-sm">
              <span className="font-bold">{t('stats.lineCount')}:</span> {textStats.lineCount}
            </div>
          </div>
        </div>

        <div>
          <div className="mb-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mb-4">
              <button
                className={`py-2 px-3 rounded text-sm ${selectedCase === 'sentence' ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => handleCaseChange('sentence')}
              >
                {t('cases.sentence')}
              </button>
              <button
                className={`py-2 px-3 rounded text-sm ${selectedCase === 'lower' ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => handleCaseChange('lower')}
              >
                {t('cases.lower')}
              </button>
              <button
                className={`py-2 px-3 rounded text-sm ${selectedCase === 'upper' ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => handleCaseChange('upper')}
              >
                {t('cases.upper')}
              </button>
              <button
                className={`py-2 px-3 rounded text-sm ${selectedCase === 'capitalized' ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => handleCaseChange('capitalized')}
              >
                {t('cases.capitalized')}
              </button>
              <button
                className={`py-2 px-3 rounded text-sm ${selectedCase === 'alternating' ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => handleCaseChange('alternating')}
              >
                {t('cases.alternating')}
              </button>
              <button
                className={`py-2 px-3 rounded text-sm ${selectedCase === 'title' ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => handleCaseChange('title')}
              >
                {t('cases.title')}
              </button>
              <button
                className={`py-2 px-3 rounded text-sm ${selectedCase === 'inverse' ? 'bg-primary text-white' : 'bg-gray-100 hover:bg-gray-200'}`}
                onClick={() => handleCaseChange('inverse')}
              >
                {t('cases.inverse')}
              </button>
            </div>
          </div>

          <Textarea
            className="w-full h-64 mb-4 p-3 border border-gray-300 rounded"
            value={convertedText}
            readOnly
          />

          <div className="flex flex-wrap gap-2">
            <button
              className="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded flex items-center"
              onClick={handleCopy}
              disabled={!convertedText}
            >
              <span className="material-icons text-sm mr-1">content_copy</span>
              {t('actions.copy')}
            </button>
            <button
              className="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded flex items-center"
              onClick={handleDownload}
              disabled={!convertedText}
            >
              <span className="material-icons text-sm mr-1">download</span>
              {t('actions.download')}
            </button>
            <button
              className="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded flex items-center"
              onClick={handleClear}
              disabled={!text}
            >
              <span className="material-icons text-sm mr-1">delete</span>
              {t('actions.clear')}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TextConverter;
