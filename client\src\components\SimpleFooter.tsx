import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const SimpleFooter = () => {
  const { t, i18n } = useTranslation();
  const currentYear = new Date().getFullYear();

  // 根据当前语言返回相应的文本
  const getLocalizedText = () => {
    switch(i18n.language) {
      case 'en':
        return {
          title: 'Text Case Converter',
          description: 'One-click text case conversion, free and easy to use',
          resources: 'Resources',
          privacyPolicy: 'Privacy Policy',
          termsOfService: 'Terms of Service',
          copyright: `© 2025 Text Case Converter. All rights reserved.`
        };
        break;
      case 'es':
        return {
          title: 'Conversión de Texto',
          description: 'Conversión de mayúsculas y minúsculas con un clic, gratis y fácil de usar',
          resources: 'Recursos',
          privacyPolicy: 'Política de Privacidad',
          termsOfService: 'Términos de Servicio',
          copyright: `© 2025 Text Case Converter. Todos los derechos reservados.`
        };
        break;
      case 'fr':
        return {
          title: 'Conversion de Texte',
          description: 'Conversion de casse de texte en un clic, gratuit et facile à utiliser',
          resources: 'Ressources',
          privacyPolicy: 'Politique de Confidentialité',
          termsOfService: 'Conditions d\'Utilisation',
          copyright: `© 2025 Text Case Converter. Tous droits réservés.`
        };
        break;
      case 'de':
        return {
          title: 'Textumwandlung',
          description: 'Textumwandlung mit einem Klick, kostenlos und einfach zu bedienen',
          resources: 'Ressourcen',
          privacyPolicy: 'Datenschutzrichtlinie',
          termsOfService: 'Nutzungsbedingungen',
          copyright: `© 2025 Text Case Converter. Alle Rechte vorbehalten.`
        };
        break;
      case 'zh':
        return {
          title: 'Text Case Converter',
          description: '一键实现文本大小写多样转换，免费易用',
          resources: '资源',
          privacyPolicy: '隐私政策',
          termsOfService: '服务条款',
          copyright: `© 2025 Text Case Converter. 保留所有权利。`
        };
        break;
      default:
        return {
          title: 'Text Case Converter',
          description: 'One-click text case conversion, free and easy to use',
          resources: 'Resources',
          privacyPolicy: 'Privacy Policy',
          termsOfService: 'Terms of Service',
          copyright: `© 2025 Text Case Converter. All rights reserved.`
        };
    }
  };

  const localizedText = getLocalizedText();

  return (
    <div className="flex flex-col">
      {/* 主要内容和资源链接区域 */}
      <div className="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-4 sm:mb-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0 md:mr-8 flex-1 text-center md:text-left">
            <div className="flex items-center mb-2 justify-center md:justify-start">
              <img
                src="/logo.svg"
                alt="Text Case Converter Logo"
                className="w-5 h-5 sm:w-6 sm:h-6 mr-2"
                width="24"
                height="24"
                loading="lazy"
              />
              <h2 className="text-lg sm:text-xl font-bold">{localizedText.title}</h2>
            </div>
            <p className="text-sm sm:text-base">{localizedText.description}</p>
          </div>

          <div className="md:text-right">
            <h3 className="text-base sm:text-lg font-semibold mb-2 text-center md:text-right">{localizedText.resources}</h3>
            <div className="flex flex-wrap justify-center md:justify-end gap-4 sm:gap-6">
              <Link
                to={i18n.language === 'en' ? '/privacy-policy' : `/${i18n.language}/privacy-policy`}
                className="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors"
              >
                {localizedText.privacyPolicy}
              </Link>
              <Link
                to={i18n.language === 'en' ? '/terms-of-service' : `/${i18n.language}/terms-of-service`}
                className="text-primary hover:text-primary-dark text-sm sm:text-base transition-colors"
              >
                {localizedText.termsOfService}
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* 版权信息区域 */}
      <div className="text-center text-gray-600 py-3 sm:py-4 border-t border-gray-200 text-xs sm:text-sm">
        <p>{localizedText.copyright}</p>
      </div>
    </div>
  );
};

export default SimpleFooter;
