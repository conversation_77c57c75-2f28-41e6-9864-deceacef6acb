{"meta": {"title": "Text Case Converter - Modern Text Case Conversion Tool", "description": "Text Case Converter is a powerful text conversion tool that helps you transform text to uppercase, lowercase, title case and more. Supports multiple languages."}, "language": {"selectLanguage": "Language"}, "navigation": {"textCaseConversion": "Text Case Converter", "specialFormatting": "Special Formatting", "codeAndData": "Code & Data", "translation": "Translation", "tools": "Tools", "settings": "Settings"}, "converter": {"title": "Text Case Converter Tool", "introduction": "Accidentally left the caps lock on and typed something, but can't be bothered to start again and retype it all?", "instructions": "Simply enter your text and choose the case you want to convert it to.", "placeholder": "Type or paste your content here..."}, "cases": {"sentence": "Sentence case", "lower": "lowercase", "upper": "UPPERCASE", "capitalized": "Capitalized Case", "alternating": "aLtErNaTiNg cAsE", "title": "Title Case", "inverse": "InVeRsE CaSe"}, "actions": {"download": "Download", "copy": "Copy", "clear": "Clear", "support": "Support This Project"}, "stats": {"characterCount": "Character Count", "wordCount": "Word Count", "sentenceCount": "Sentence Count", "lineCount": "Line Count"}, "explanations": {"sectionTitle": "Text Case Converter Types", "sentence": {"title": "Sentence case", "description1": "The sentence case converter will allow you to paste any text you'd like, and it will automatically transform it to a fully formed structured sentence.", "description2": "It works by capitalizing the very first letter in each sentence, and will then go on to transform the rest of the text into lowercase as well as converting i's into I's.", "example": "This is an example of sentence case."}, "lower": {"title": "lowercase", "description": "If you are wondering how to uncapitalize text, this is exactly what the lower case text converter will allow you to do - it transforms all the letters in your text into lowercase letters.", "example": "this is an example of lower case."}, "upper": {"title": "UPPERCASE", "description": "The upper case transformer will take any text that you have and will generate all the letters into upper case ones. It will essentially make all lower case letters into CAPITALS.", "example": "THIS IS AN EXAMPLE OF UPPER CASE."}, "capitalized": {"title": "Capitalized Case", "description": "The capitalized case converter will automatically convert the starting letter of every word into an upper case and will leave the remaining letters as lower case ones.", "example": "This Is An Example Of Capitalized Case."}, "alternating": {"title": "aLtErNaTiNg cAsE", "description": "The alternating case converter will allow you to transform your text into text that alternates between lower case and upper case. It will generate a capital letter and then a lower case letter within the same word.", "example": "tHiS Is aN ExAmPlE Of aLtErNaTiNg cAsE."}, "title": {"title": "Title Case", "description": "The title case converter is perfect for those who are a bit unsure on how to title an upcoming essay. It ensures the correct letters are capitalized within the context of a title.", "example": "This Is an Example of Title Case."}}, "additionalTools": {"title": "Additional Text Tools", "viewAll": "View all text tools", "reverse": {"title": "Reverse Text Generator", "description": "Flip your text backwards"}, "upsideDown": {"title": "Upside Down Text", "description": "Flip your text upside down"}, "morseCode": {"title": "Morse Code Translator", "description": "Convert text to morse code"}, "smallText": {"title": "Small Text Generator", "description": "Create tiny stylized text"}, "strikethrough": {"title": "Strikethrough Text", "description": "Add a strike through your text"}, "wideText": {"title": "Wide Text Generator", "description": "Create aesthetically spaced text"}}, "faq": {"title": "Frequently Asked Questions", "why": {"question": "Why would I need to convert text case?", "answer": "Text case conversion is useful for formatting documents, preparing titles, fixing accidentally capitalized text, or maintaining consistent styling in your content."}, "secure": {"question": "Is my text data secure when using this tool?", "answer": "Absolutely. All text processing happens directly in your browser. We never store, transmit, or access the text you enter."}, "mobile": {"question": "Can I use this tool on my mobile device?", "answer": "Yes, our text case converter is fully responsive and works on all devices including smartphones, tablets, and desktop computers."}, "languages": {"question": "Does this tool work with non-English languages?", "answer": "Yes, our tool supports multiple languages and international character sets including accented characters, Cyrillic, Greek, and many others."}}, "footer": {"description": "A comprehensive collection of text transformation tools to help you format and optimize your content.", "popularTools": "Popular Tools", "resources": "Resources", "language": "Language", "allRightsReserved": "All rights reserved.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "contactUs": "Contact Us"}, "tools": {"textCaseConverter": "Text Case Converter", "loremIpsumGenerator": "Lorem Ipsum Generator", "characterCountTool": "Character Count <PERSON>", "wordCountTool": "Word Count Tool", "reverseTextGenerator": "Reverse Text Generator"}, "resources": {"blog": "Blog", "helpCenter": "Help Center", "apiDocumentation": "API Documentation", "suggestTool": "Suggest a Tool", "chromeExtension": "Chrome Extension"}, "toast": {"copied": "Text Copied", "copiedDescription": "Text has been copied to clipboard", "copyFailed": "<PERSON><PERSON> Failed", "copyFailedDescription": "Unable to copy text to clipboard", "downloaded": "Downloaded", "downloadedDescription": "Text has been downloaded as a file", "cleared": "Cleared", "clearedDescription": "Text has been cleared"}}