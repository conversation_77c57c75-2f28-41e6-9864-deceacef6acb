import fs from 'fs';
import path from 'path';

// 网站配置
const config = {
  siteUrl: 'https://textcase.top',
  outputDir: './dist/public',
  clientPublicDir: './client/public',
  lastmod: new Date().toISOString().split('T')[0],
  languages: ['en', 'es', 'fr', 'de', 'zh'],
  pages: [
    {
      path: '/',
      changefreq: 'weekly',
      priority: '1.0'
    },
    {
      path: '/privacy-policy',
      changefreq: 'monthly',
      priority: '0.8'
    },
    {
      path: '/terms-of-service',
      changefreq: 'monthly',
      priority: '0.8'
    }
  ],
  excludePaths: ['/404', '/api/'],
};

// 确保输出目录存在
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// 生成网站地图
function generateSitemap() {
  const { siteUrl, outputDir, pages, languages, lastmod } = config;

  ensureDirectoryExists(outputDir);

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
`;

  // 为每个页面生成所有语言版本的 URL
  pages.forEach(page => {
    // 为每种语言生成 URL
    languages.forEach(lang => {
      const langPath = lang === 'en' ? '' : `/${lang}`;
      const pagePath = page.path === '/' ? '' : page.path;
      const fullPath = `${langPath}${pagePath}`;
      const fullUrl = `${siteUrl}${fullPath}`;

      sitemap += `  <url>
    <loc>${fullUrl}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${lang === 'en' ? page.priority : (parseFloat(page.priority) * 0.9).toFixed(1)}</priority>
    <!-- 多语言链接 -->
`;

      // 添加所有语言版本的链接
      languages.forEach(l => {
        const lPath = l === 'en' ? '' : `/${l}`;
        sitemap += `    <xhtml:link rel="alternate" hreflang="${l}" href="${siteUrl}${lPath}${pagePath}" />\n`;
      });

      // 添加默认语言链接
      sitemap += `    <xhtml:link rel="alternate" hreflang="x-default" href="${siteUrl}${pagePath}" />
  </url>

`;
    });
  });

  sitemap += `</urlset>
`;

  // 写入文件
  fs.writeFileSync(path.join(outputDir, 'sitemap.xml'), sitemap);
  console.log(`✅ 网站地图已生成: ${path.join(outputDir, 'sitemap.xml')}`);

  // 同时复制到客户端公共目录（用于开发环境）
  fs.writeFileSync(path.join(config.clientPublicDir, 'sitemap.xml'), sitemap);
  console.log(`✅ 网站地图已复制到客户端目录: ${path.join(config.clientPublicDir, 'sitemap.xml')}`);
}

// 生成 robots.txt
function generateRobotsTxt() {
  const { siteUrl, outputDir, excludePaths } = config;

  ensureDirectoryExists(outputDir);

  let robotsTxt = `User-agent: *
Allow: /

`;

  // 添加禁止访问的路径
  excludePaths.forEach(path => {
    if (path.startsWith('/')) {
      robotsTxt += `Disallow: ${path}\n`;
    }
  });

  robotsTxt += `
# 网站地图链接
Sitemap: ${siteUrl}/sitemap.xml
`;

  // 写入文件
  fs.writeFileSync(path.join(outputDir, 'robots.txt'), robotsTxt);
  console.log(`✅ robots.txt 已生成: ${path.join(outputDir, 'robots.txt')}`);

  // 同时复制到客户端公共目录（用于开发环境）
  fs.writeFileSync(path.join(config.clientPublicDir, 'robots.txt'), robotsTxt);
  console.log(`✅ robots.txt 已复制到客户端目录: ${path.join(config.clientPublicDir, 'robots.txt')}`);
}

// 执行生成
generateSitemap();
generateRobotsTxt();

console.log('✅ 网站地图和 robots.txt 生成完成！');
